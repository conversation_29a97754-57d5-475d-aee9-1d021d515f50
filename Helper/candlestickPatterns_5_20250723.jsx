// Crypto Trading Assistant - Auto-generated Component #5
// Created: 2025-07-23 02:35:30
// File: candlestickPatterns_5_20250723.jsx
// Purpose: Candlestick Patterns Analysis Module
// Compatible with: Binance API, CoinGecko API, React Frontend

import React, { useState, useEffect } from 'react';

const CandlestickPatternDetector = {
    detectDoji(candle) {
        const bodySize = Math.abs(candle.close - candle.open);
        const totalRange = candle.high - candle.low;
        return bodySize / totalRange < 0.1;
    },
    
    detectHammer(candle) {
        const bodySize = Math.abs(candle.close - candle.open);
        const lowerShadow = Math.min(candle.open, candle.close) - candle.low;
        const upperShadow = candle.high - Math.max(candle.open, candle.close);
        
        return lowerShadow > bodySize * 2 && upperShadow < bodySize * 0.5;
    },
    
    detectEngulfing(candle1, candle2) {
        const bullishEngulfing = candle1.close < candle1.open && 
                                candle2.close > candle2.open &&
                                candle2.open < candle1.close &&
                                candle2.close > candle1.open;
        
        const bearishEngulfing = candle1.close > candle1.open && 
                                candle2.close < candle2.open &&
                                candle2.open > candle1.close &&
                                candle2.close < candle1.open;
        
        return { bullishEngulfing, bearishEngulfing };
    },
    
    detectMorningStar(candle1, candle2, candle3) {
        return candle1.close < candle1.open && // First candle is bearish
               Math.abs(candle2.close - candle2.open) < (candle1.open - candle1.close) * 0.3 && // Small body
               candle3.close > candle3.open && // Third candle is bullish
               candle3.close > (candle1.open + candle1.close) / 2; // Closes above midpoint
    },
    
    detectEveningStar(candle1, candle2, candle3) {
        return candle1.close > candle1.open && // First candle is bullish
               Math.abs(candle2.close - candle2.open) < (candle1.close - candle1.open) * 0.3 && // Small body
               candle3.close < candle3.open && // Third candle is bearish
               candle3.close < (candle1.open + candle1.close) / 2; // Closes below midpoint
    }
};

const PatternAnalyzer = () => {
    const [detectedPatterns, setDetectedPatterns] = useState([
        {
            pattern: 'Bullish Engulfing',
            symbol: 'BTC',
            timeframe: '15m',
            confidence: 85,
            signal: 'BUY',
            timestamp: new Date().toISOString(),
            description: 'Strong bullish reversal pattern detected'
        },
        {
            pattern: 'Doji',
            symbol: 'ETH',
            timeframe: '1h',
            confidence: 72,
            signal: 'NEUTRAL',
            timestamp: new Date().toISOString(),
            description: 'Indecision in the market, potential reversal'
        },
        {
            pattern: 'Hammer',
            symbol: 'ADA',
            timeframe: '30m',
            confidence: 78,
            signal: 'BUY',
            timestamp: new Date().toISOString(),
            description: 'Bullish reversal pattern at support level'
        },
        {
            pattern: 'Evening Star',
            symbol: 'SOL',
            timeframe: '4h',
            confidence: 91,
            signal: 'SELL',
            timestamp: new Date().toISOString(),
            description: 'Strong bearish reversal pattern confirmed'
        }
    ]);
    
    const [selectedTimeframe, setSelectedTimeframe] = useState('15m');
    const [minConfidence, setMinConfidence] = useState(70);
    
    const filteredPatterns = detectedPatterns.filter(pattern => 
        pattern.timeframe === selectedTimeframe && pattern.confidence >= minConfidence
    );
    
    return (
        <div className="pattern-analyzer">
            <h2>Candlestick Pattern Analysis</h2>
            
            <div className="controls">
                <div className="control-group">
                    <label>Timeframe:</label>
                    <select value={selectedTimeframe} onChange={(e) => setSelectedTimeframe(e.target.value)}>
                        <option value="1m">1 Minute</option>
                        <option value="5m">5 Minutes</option>
                        <option value="15m">15 Minutes</option>
                        <option value="30m">30 Minutes</option>
                        <option value="1h">1 Hour</option>
                        <option value="4h">4 Hours</option>
                        <option value="1d">1 Day</option>
                    </select>
                </div>
                
                <div className="control-group">
                    <label>Min Confidence:</label>
                    <input 
                        type="range" 
                        min="50" 
                        max="100" 
                        value={minConfidence}
                        onChange={(e) => setMinConfidence(parseInt(e.target.value))}
                    />
                    <span>{minConfidence}%</span>
                </div>
            </div>
            
            <div className="patterns-list">
                {filteredPatterns.map((pattern, index) => (
                    <div key={index} className={`pattern-card ${pattern.signal.toLowerCase()}`}>
                        <div className="pattern-header">
                            <h3>{pattern.pattern}</h3>
                            <span className="symbol">{pattern.symbol}</span>
                            <span className="confidence">{pattern.confidence}%</span>
                        </div>
                        <div className="pattern-details">
                            <p className="signal">Signal: <strong>{pattern.signal}</strong></p>
                            <p className="timeframe">Timeframe: {pattern.timeframe}</p>
                            <p className="description">{pattern.description}</p>
                            <p className="timestamp">
                                Detected: {new Date(pattern.timestamp).toLocaleString()}
                            </p>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

const CandlestickChart = () => {
    const [candleData, setCandleData] = useState([
        { open: 42000, high: 42500, low: 41800, close: 42350, volume: 1500000, timestamp: Date.now() - 900000 },
        { open: 42350, high: 42800, low: 42100, close: 42650, volume: 1800000, timestamp: Date.now() - 600000 },
        { open: 42650, high: 42900, low: 42400, close: 42450, volume: 1200000, timestamp: Date.now() - 300000 },
        { open: 42450, high: 42700, low: 42200, close: 42580, volume: 1600000, timestamp: Date.now() }
    ]);
    
    const [selectedSymbol, setSelectedSymbol] = useState('BTC');
    
    const renderCandle = (candle, index) => {
        const isBullish = candle.close > candle.open;
        const bodyHeight = Math.abs(candle.close - candle.open);
        const wickTop = candle.high - Math.max(candle.open, candle.close);
        const wickBottom = Math.min(candle.open, candle.close) - candle.low;
        
        return (
            <div key={index} className={`candle ${isBullish ? 'bullish' : 'bearish'}`}>
                <div className="wick-top" style={{ height: `${wickTop / 10}px` }}></div>
                <div className="body" style={{ height: `${bodyHeight / 10}px` }}>
                    <span className="price-info">
                        O: {candle.open} H: {candle.high} L: {candle.low} C: {candle.close}
                    </span>
                </div>
                <div className="wick-bottom" style={{ height: `${wickBottom / 10}px` }}></div>
                <div className="volume-bar" style={{ height: `${candle.volume / 50000}px` }}></div>
            </div>
        );
    };
    
    return (
        <div className="candlestick-chart">
            <h3>Candlestick Chart - {selectedSymbol}</h3>
            <div className="chart-controls">
                <select value={selectedSymbol} onChange={(e) => setSelectedSymbol(e.target.value)}>
                    <option value="BTC">Bitcoin (BTC)</option>
                    <option value="ETH">Ethereum (ETH)</option>
                    <option value="ADA">Cardano (ADA)</option>
                    <option value="SOL">Solana (SOL)</option>
                </select>
            </div>
            <div className="chart-container">
                <div className="price-axis">
                    <div>43000</div>
                    <div>42500</div>
                    <div>42000</div>
                    <div>41500</div>
                </div>
                <div className="candles-container">
                    {candleData.map((candle, index) => renderCandle(candle, index))}
                </div>
                <div className="time-axis">
                    <div>15:00</div>
                    <div>15:15</div>
                    <div>15:30</div>
                    <div>15:45</div>
                </div>
            </div>
        </div>
    );
};

const PatternEducation = () => {
    const patterns = [
        {
            name: 'Doji',
            type: 'Reversal',
            description: 'A candle with a very small body, indicating indecision in the market.',
            bullishSignal: 'When found at the bottom of a downtrend',
            bearishSignal: 'When found at the top of an uptrend',
            reliability: 'Medium'
        },
        {
            name: 'Hammer',
            type: 'Bullish Reversal',
            description: 'A candle with a small body and long lower shadow, resembling a hammer.',
            bullishSignal: 'Strong bullish reversal signal at support levels',
            bearishSignal: 'N/A',
            reliability: 'High'
        },
        {
            name: 'Engulfing',
            type: 'Reversal',
            description: 'A two-candle pattern where the second candle completely engulfs the first.',
            bullishSignal: 'Bullish engulfing at support levels',
            bearishSignal: 'Bearish engulfing at resistance levels',
            reliability: 'High'
        },
        {
            name: 'Morning Star',
            type: 'Bullish Reversal',
            description: 'A three-candle pattern indicating a potential bullish reversal.',
            bullishSignal: 'Strong bullish reversal signal',
            bearishSignal: 'N/A',
            reliability: 'Very High'
        }
    ];
    
    return (
        <div className="pattern-education">
            <h3>Candlestick Pattern Guide</h3>
            <div className="patterns-grid">
                {patterns.map((pattern, index) => (
                    <div key={index} className="pattern-info-card">
                        <h4>{pattern.name}</h4>
                        <p className="pattern-type">{pattern.type}</p>
                        <p className="description">{pattern.description}</p>
                        <div className="signals">
                            {pattern.bullishSignal !== 'N/A' && (
                                <p className="bullish-signal">
                                    <strong>Bullish:</strong> {pattern.bullishSignal}
                                </p>
                            )}
                            {pattern.bearishSignal !== 'N/A' && (
                                <p className="bearish-signal">
                                    <strong>Bearish:</strong> {pattern.bearishSignal}
                                </p>
                            )}
                        </div>
                        <p className="reliability">
                            <strong>Reliability:</strong> {pattern.reliability}
                        </p>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default candlestickPatternsComponent;
