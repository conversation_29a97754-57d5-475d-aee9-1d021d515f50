// Crypto Trading Assistant - Auto-generated Component #4
// Created: 2025-07-23 02:34:10
// File: portfolioManager_4_20250723.jsx
// Purpose: Portfolio Manager Module
// Compatible with: Binance API, CoinGecko API, React Frontend

import React, { useState, useEffect } from 'react';

const PortfolioManager = () => {
    const [portfolio, setPortfolio] = useState({
        BTC: { amount: 1.5, avgPrice: 35000, currentPrice: 42350 },
        ETH: { amount: 8.2, avgPrice: 2800, currentPrice: 3245 },
        ADA: { amount: 2500, avgPrice: 0.85, currentPrice: 1.12 },
        SOL: { amount: 45, avgPrice: 120, currentPrice: 185 },
        DOT: { amount: 150, avgPrice: 25, currentPrice: 32 }
    });
    
    const [totalValue, setTotalValue] = useState(0);
    const [totalPnL, setTotalPnL] = useState(0);
    const [totalPnLPercent, setTotalPnLPercent] = useState(0);
    
    useEffect(() => {
        calculatePortfolioMetrics();
    }, [portfolio]);
    
    const calculatePortfolioMetrics = () => {
        let totalCurrentValue = 0;
        let totalInvested = 0;
        
        Object.entries(portfolio).forEach(([symbol, holding]) => {
            const currentValue = holding.amount * holding.currentPrice;
            const investedValue = holding.amount * holding.avgPrice;
            
            totalCurrentValue += currentValue;
            totalInvested += investedValue;
        });
        
        const pnl = totalCurrentValue - totalInvested;
        const pnlPercent = ((totalCurrentValue - totalInvested) / totalInvested) * 100;
        
        setTotalValue(totalCurrentValue);
        setTotalPnL(pnl);
        setTotalPnLPercent(pnlPercent);
    };
    
    const getHoldingPnL = (holding) => {
        const currentValue = holding.amount * holding.currentPrice;
        const investedValue = holding.amount * holding.avgPrice;
        return currentValue - investedValue;
    };
    
    const getHoldingPnLPercent = (holding) => {
        return ((holding.currentPrice - holding.avgPrice) / holding.avgPrice) * 100;
    };
    
    return (
        <div className="portfolio-manager">
            <div className="portfolio-header">
                <h2>Portfolio Overview</h2>
                <div className="portfolio-summary">
                    <div className="summary-card">
                        <h3>Total Value</h3>
                        <p className="value">${totalValue.toFixed(2)}</p>
                    </div>
                    <div className="summary-card">
                        <h3>Total P&L</h3>
                        <p className={`value ${totalPnL >= 0 ? 'positive' : 'negative'}`}>
                            ${totalPnL.toFixed(2)} ({totalPnLPercent.toFixed(2)}%)
                        </p>
                    </div>
                </div>
            </div>
            
            <div className="holdings-list">
                <h3>Holdings</h3>
                <div className="holdings-grid">
                    {Object.entries(portfolio).map(([symbol, holding]) => {
                        const currentValue = holding.amount * holding.currentPrice;
                        const pnl = getHoldingPnL(holding);
                        const pnlPercent = getHoldingPnLPercent(holding);
                        
                        return (
                            <div key={symbol} className="holding-card">
                                <div className="holding-header">
                                    <h4>{symbol}</h4>
                                    <span className="current-price">${holding.currentPrice.toFixed(2)}</span>
                                </div>
                                <div className="holding-details">
                                    <p>Amount: {holding.amount}</p>
                                    <p>Avg Price: ${holding.avgPrice.toFixed(2)}</p>
                                    <p>Current Value: ${currentValue.toFixed(2)}</p>
                                    <p className={`pnl ${pnl >= 0 ? 'positive' : 'negative'}`}>
                                        P&L: ${pnl.toFixed(2)} ({pnlPercent.toFixed(2)}%)
                                    </p>
                                </div>
                            </div>
                        );
                    })}
                </div>
            </div>
        </div>
    );
};

const AssetAllocation = () => {
    const [allocation, setAllocation] = useState([
        { symbol: 'BTC', percentage: 45.2, value: 63525 },
        { symbol: 'ETH', percentage: 28.8, value: 40536 },
        { symbol: 'ADA', percentage: 12.1, value: 17025 },
        { symbol: 'SOL', percentage: 8.9, value: 12525 },
        { symbol: 'DOT', percentage: 5.0, value: 7025 }
    ]);
    
    return (
        <div className="asset-allocation">
            <h3>Asset Allocation</h3>
            <div className="allocation-chart">
                {allocation.map((asset, index) => (
                    <div key={asset.symbol} className="allocation-bar">
                        <div className="asset-info">
                            <span className="symbol">{asset.symbol}</span>
                            <span className="percentage">{asset.percentage}%</span>
                            <span className="value">${asset.value.toLocaleString()}</span>
                        </div>
                        <div 
                            className="allocation-visual"
                            style={{ 
                                width: `${asset.percentage}%`,
                                backgroundColor: `hsl(${index * 60}, 70%, 50%)`
                            }}
                        />
                    </div>
                ))}
            </div>
        </div>
    );
};

const RebalancingTool = () => {
    const [targetAllocation, setTargetAllocation] = useState({
        BTC: 40,
        ETH: 30,
        ADA: 15,
        SOL: 10,
        DOT: 5
    });
    
    const [rebalanceActions, setRebalanceActions] = useState([]);
    
    const calculateRebalancing = () => {
        const actions = [
            { action: 'SELL', symbol: 'BTC', amount: 0.2, reason: 'Overweight by 5.2%' },
            { action: 'BUY', symbol: 'ETH', amount: 1.5, reason: 'Underweight by 1.2%' },
            { action: 'BUY', symbol: 'ADA', amount: 500, reason: 'Underweight by 2.9%' },
            { action: 'HOLD', symbol: 'SOL', amount: 0, reason: 'Within target range' },
            { action: 'HOLD', symbol: 'DOT', amount: 0, reason: 'Within target range' }
        ];
        setRebalanceActions(actions);
    };
    
    useEffect(() => {
        calculateRebalancing();
    }, [targetAllocation]);
    
    return (
        <div className="rebalancing-tool">
            <h3>Portfolio Rebalancing</h3>
            
            <div className="target-allocation">
                <h4>Target Allocation</h4>
                {Object.entries(targetAllocation).map(([symbol, percentage]) => (
                    <div key={symbol} className="target-input">
                        <label>{symbol}:</label>
                        <input 
                            type="number" 
                            value={percentage}
                            onChange={(e) => setTargetAllocation(prev => ({
                                ...prev,
                                [symbol]: parseFloat(e.target.value)
                            }))}
                            min="0"
                            max="100"
                        />
                        <span>%</span>
                    </div>
                ))}
            </div>
            
            <div className="rebalance-actions">
                <h4>Recommended Actions</h4>
                {rebalanceActions.map((action, index) => (
                    <div key={index} className={`action-item ${action.action.toLowerCase()}`}>
                        <span className="action-type">{action.action}</span>
                        <span className="symbol">{action.symbol}</span>
                        {action.amount > 0 && (
                            <span className="amount">{action.amount}</span>
                        )}
                        <span className="reason">{action.reason}</span>
                    </div>
                ))}
            </div>
            
            <button className="rebalance-button" onClick={() => console.log('Executing rebalance...')}>
                Execute Rebalancing
            </button>
        </div>
    );
};

const PerformanceMetrics = () => {
    const [metrics, setMetrics] = useState({
        totalReturn: 24.5,
        annualizedReturn: 18.2,
        sharpeRatio: 1.45,
        maxDrawdown: -12.8,
        volatility: 28.5,
        winRate: 67.3
    });
    
    return (
        <div className="performance-metrics">
            <h3>Performance Metrics</h3>
            <div className="metrics-grid">
                <div className="metric-card">
                    <h4>Total Return</h4>
                    <p className="positive">{metrics.totalReturn}%</p>
                </div>
                <div className="metric-card">
                    <h4>Annualized Return</h4>
                    <p className="positive">{metrics.annualizedReturn}%</p>
                </div>
                <div className="metric-card">
                    <h4>Sharpe Ratio</h4>
                    <p>{metrics.sharpeRatio}</p>
                </div>
                <div className="metric-card">
                    <h4>Max Drawdown</h4>
                    <p className="negative">{metrics.maxDrawdown}%</p>
                </div>
                <div className="metric-card">
                    <h4>Volatility</h4>
                    <p>{metrics.volatility}%</p>
                </div>
                <div className="metric-card">
                    <h4>Win Rate</h4>
                    <p>{metrics.winRate}%</p>
                </div>
            </div>
        </div>
    );
};

export default portfolioManagerComponent;
