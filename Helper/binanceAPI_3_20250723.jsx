// Crypto Trading Assistant - Auto-generated Component #3
// Created: 2025-07-23 02:32:45
// File: binanceAPI_3_20250723.jsx
// Purpose: Binance API Integration Module
// Compatible with: Binance API, CoinGecko API, React Frontend

import React, { useState, useEffect } from 'react';

const BinanceAPIClient = {
    baseURL: 'https://api.binance.com/api/v3',
    
    async getTickerPrice(symbol = 'BTCUSDT') {
        try {
            const response = await fetch(`${this.baseURL}/ticker/price?symbol=${symbol}`);
            const data = await response.json();
            return {
                symbol: data.symbol,
                price: parseFloat(data.price),
                timestamp: Date.now()
            };
        } catch (error) {
            console.error('Binance API Error:', error);
            throw error;
        }
    },
    
    async get24hrStats(symbol = 'ETHUSDT') {
        try {
            const response = await fetch(`${this.baseURL}/ticker/24hr?symbol=${symbol}`);
            const data = await response.json();
            return {
                symbol: data.symbol,
                priceChange: parseFloat(data.priceChange),
                priceChangePercent: parseFloat(data.priceChangePercent),
                volume: parseFloat(data.volume),
                high: parseFloat(data.highPrice),
                low: parseFloat(data.lowPrice),
                openPrice: parseFloat(data.openPrice),
                lastPrice: parseFloat(data.lastPrice)
            };
        } catch (error) {
            console.error('24hr Stats Error:', error);
            throw error;
        }
    },
    
    async getCandlestickData(symbol = 'ADAUSDT', interval = '15m', limit = 100) {
        try {
            const response = await fetch(`${this.baseURL}/klines?symbol=${symbol}&interval=${interval}&limit=${limit}`);
            const data = await response.json();
            return data.map(candle => ({
                openTime: candle[0],
                open: parseFloat(candle[1]),
                high: parseFloat(candle[2]),
                low: parseFloat(candle[3]),
                close: parseFloat(candle[4]),
                volume: parseFloat(candle[5]),
                closeTime: candle[6]
            }));
        } catch (error) {
            console.error('Candlestick Data Error:', error);
            throw error;
        }
    }
};

const LivePriceTracker = () => {
    const [prices, setPrices] = useState({});
    const [loading, setLoading] = useState(true);
    const symbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'SOLUSDT', 'DOTUSDT'];
    
    useEffect(() => {
        const fetchPrices = async () => {
            try {
                const pricePromises = symbols.map(symbol => 
                    BinanceAPIClient.getTickerPrice(symbol)
                );
                const priceData = await Promise.all(pricePromises);
                
                const priceMap = {};
                priceData.forEach(data => {
                    priceMap[data.symbol] = data.price;
                });
                
                setPrices(priceMap);
                setLoading(false);
            } catch (error) {
                console.error('Failed to fetch prices:', error);
                setLoading(false);
            }
        };
        
        fetchPrices();
        const interval = setInterval(fetchPrices, 10000); // Update every 10 seconds
        
        return () => clearInterval(interval);
    }, []);
    
    if (loading) return <div className="loading">Loading prices...</div>;
    
    return (
        <div className="live-price-tracker">
            <h2>Live Crypto Prices</h2>
            <div className="price-grid">
                {Object.entries(prices).map(([symbol, price]) => (
                    <div key={symbol} className="price-card">
                        <h3>{symbol.replace('USDT', '')}</h3>
                        <p className="price">${price.toFixed(2)}</p>
                        <p className="timestamp">Updated: {new Date().toLocaleTimeString()}</p>
                    </div>
                ))}
            </div>
        </div>
    );
};

const OrderBookComponent = () => {
    const [orderBook, setOrderBook] = useState({
        bids: [],
        asks: [],
        lastUpdateId: 0
    });
    
    useEffect(() => {
        // Simulate order book data
        const generateOrderBook = () => {
            const basePrice = 42000;
            const bids = [];
            const asks = [];
            
            for (let i = 0; i < 10; i++) {
                bids.push({
                    price: basePrice - (i * 10),
                    quantity: Math.random() * 5,
                    total: 0
                });
                asks.push({
                    price: basePrice + (i * 10),
                    quantity: Math.random() * 5,
                    total: 0
                });
            }
            
            setOrderBook({ bids, asks, lastUpdateId: Date.now() });
        };
        
        generateOrderBook();
        const interval = setInterval(generateOrderBook, 2000);
        
        return () => clearInterval(interval);
    }, []);
    
    return (
        <div className="order-book">
            <h3>Order Book (BTC/USDT)</h3>
            <div className="order-book-container">
                <div className="asks">
                    <h4>Asks (Sell Orders)</h4>
                    {orderBook.asks.map((ask, index) => (
                        <div key={index} className="order-row ask">
                            <span className="price">${ask.price.toFixed(2)}</span>
                            <span className="quantity">{ask.quantity.toFixed(4)}</span>
                        </div>
                    ))}
                </div>
                <div className="spread">
                    <p>Spread: ${(orderBook.asks[0]?.price - orderBook.bids[0]?.price).toFixed(2)}</p>
                </div>
                <div className="bids">
                    <h4>Bids (Buy Orders)</h4>
                    {orderBook.bids.map((bid, index) => (
                        <div key={index} className="order-row bid">
                            <span className="price">${bid.price.toFixed(2)}</span>
                            <span className="quantity">{bid.quantity.toFixed(4)}</span>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
};

const TradingPairSelector = () => {
    const [selectedPair, setSelectedPair] = useState('BTCUSDT');
    const [pairStats, setPairStats] = useState(null);
    
    const tradingPairs = [
        'BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'SOLUSDT', 'DOTUSDT',
        'LINKUSDT', 'MATICUSDT', 'AVAXUSDT', 'UNIUSDT', 'ATOMUSDT'
    ];
    
    useEffect(() => {
        const fetchPairStats = async () => {
            try {
                const stats = await BinanceAPIClient.get24hrStats(selectedPair);
                setPairStats(stats);
            } catch (error) {
                console.error('Failed to fetch pair stats:', error);
            }
        };
        
        fetchPairStats();
    }, [selectedPair]);
    
    return (
        <div className="trading-pair-selector">
            <h3>Trading Pair Analysis</h3>
            <select 
                value={selectedPair} 
                onChange={(e) => setSelectedPair(e.target.value)}
                className="pair-selector"
            >
                {tradingPairs.map(pair => (
                    <option key={pair} value={pair}>{pair}</option>
                ))}
            </select>
            
            {pairStats && (
                <div className="pair-stats">
                    <h4>{pairStats.symbol}</h4>
                    <div className="stats-grid">
                        <div className="stat">
                            <label>Last Price:</label>
                            <span>${pairStats.lastPrice.toFixed(2)}</span>
                        </div>
                        <div className="stat">
                            <label>24h Change:</label>
                            <span className={pairStats.priceChangePercent >= 0 ? 'positive' : 'negative'}>
                                {pairStats.priceChangePercent.toFixed(2)}%
                            </span>
                        </div>
                        <div className="stat">
                            <label>24h High:</label>
                            <span>${pairStats.high.toFixed(2)}</span>
                        </div>
                        <div className="stat">
                            <label>24h Low:</label>
                            <span>${pairStats.low.toFixed(2)}</span>
                        </div>
                        <div className="stat">
                            <label>24h Volume:</label>
                            <span>{pairStats.volume.toLocaleString()}</span>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default binanceAPIComponent;
