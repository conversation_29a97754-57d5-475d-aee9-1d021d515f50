import random
import time
import threading
import pyautogui
import keyboard
import os
from collections import deque
from datetime import datetime

class RandomJSFileGenerator:
    def __init__(self):
        self.running = True
        self.generated_code = deque(maxlen=1000)  # Track last 1000 snippets
        self.file_counter = 1
        self.helper_folder = "Helper"
        
        # Ensure Helper folder exists
        if not os.path.exists(self.helper_folder):
            os.makedirs(self.helper_folder)
        
        # Crypto Trading Assistant specific templates
        self.crypto_variables = ['price', 'volume', 'marketCap', 'signal', 'indicator', 'candlestick', 'trend', 'portfolio', 'balance', 'order', 'trade', 'symbol', 'exchange', 'timeframe', 'rsi', 'macd', 'sma', 'ema', 'bollinger', 'support', 'resistance']
        self.crypto_functions = ['calculateRSI', 'getMarketData', 'analyzeTrend', 'generateSignal', 'executeTrade', 'updatePortfolio', 'fetchCandlesticks', 'calculateSMA', 'calculateEMA', 'detectPattern', 'checkSupport', 'checkResistance']
        self.crypto_symbols = ['BTC', 'ETH', 'ADA', 'SOL', 'DOT', 'LINK', 'MATIC', 'AVAX', 'UNI', 'ATOM', 'XRP', 'LTC', 'BCH', 'BNB', 'DOGE']
        self.timeframes = ['1m', '5m', '15m', '30m', '1h', '4h', '1d', '1w']
        self.indicators = ['RSI', 'MACD', 'SMA', 'EMA', 'BB', 'STOCH', 'ADX', 'CCI', 'MFI', 'OBV']
        self.signal_types = ['BUY', 'SELL', 'HOLD', 'STRONG_BUY', 'STRONG_SELL']
        self.api_endpoints = ['/api/market-data', '/api/signals', '/api/portfolio', '/api/trades', '/api/indicators', '/api/candlesticks']
        self.variables = self.crypto_variables + ['data', 'result', 'config', 'response', 'api', 'cache', 'state']
        self.functions = self.crypto_functions + ['console.log', 'fetch', 'parseInt', 'parseFloat', 'JSON.stringify', 'JSON.parse', 'setTimeout', 'setInterval']
        self.methods = ['push', 'pop', 'slice', 'map', 'filter', 'reduce', 'forEach', 'find', 'includes', 'indexOf', 'join', 'split']
        self.operators = ['+', '-', '*', '/', '%', '==', '!=', '>', '<', '>=', '<=', '&&', '||', '===', '!==']
        self.keywords = ['const', 'let', 'var', 'function', 'return', 'if', 'else', 'for', 'while', 'try', 'catch', 'async', 'await']
        
    def generate_complex_js_code(self):
        """Generate crypto trading assistant specific JavaScript code"""
        code_snippets = []

        # Generate multiple related code snippets for a single file
        num_snippets = random.randint(4, 10)

        for _ in range(num_snippets):
            code_type = random.choice(['crypto_variable', 'crypto_function', 'api_call', 'trading_logic', 'indicator_calc', 'signal_generation', 'portfolio_management', 'market_analysis', 'crypto_class', 'websocket_handler'])
            
            if code_type == 'crypto_variable':
                keyword = random.choice(['const', 'let'])
                var_name = random.choice(self.crypto_variables) + str(random.randint(1, 99))
                value_type = random.choice(['price', 'crypto_array', 'trading_config', 'signal_data'])

                if value_type == 'price':
                    price = round(random.uniform(0.001, 100000), 6)
                    value = str(price)
                elif value_type == 'crypto_array':
                    symbols = random.sample(self.crypto_symbols, random.randint(3, 6))
                    value = f'[{", ".join([f\'"{s}"\' for s in symbols])}]'
                elif value_type == 'trading_config':
                    config_props = []
                    config_props.append(f'symbol: "{random.choice(self.crypto_symbols)}"')
                    config_props.append(f'timeframe: "{random.choice(self.timeframes)}"')
                    config_props.append(f'indicator: "{random.choice(self.indicators)}"')
                    config_props.append(f'threshold: {random.uniform(0.1, 10):.2f}')
                    value = f'{{{", ".join(config_props)}}}'
                else:  # signal_data
                    signal_props = []
                    signal_props.append(f'type: "{random.choice(self.signal_types)}"')
                    signal_props.append(f'confidence: {random.uniform(0.5, 1.0):.2f}')
                    signal_props.append(f'timestamp: Date.now()')
                    signal_props.append(f'price: {round(random.uniform(100, 50000), 2)}')
                    value = f'{{{", ".join(signal_props)}}}'

                code = f"{keyword} {var_name} = {value};"
                
            elif code_type == 'crypto_function':
                func_name = random.choice(self.crypto_functions) + str(random.randint(1, 99))
                symbol = random.choice(self.crypto_symbols)
                timeframe = random.choice(self.timeframes)
                if 'calculate' in func_name.lower():
                    code = f"function {func_name}(prices, period = 14) {{\n    // Calculate {random.choice(self.indicators)} for {symbol}\n    const sum = prices.slice(-period).reduce((a, b) => a + b, 0);\n    return sum / period;\n}}"
                elif 'fetch' in func_name.lower():
                    code = f"async function {func_name}(symbol = '{symbol}', timeframe = '{timeframe}') {{\n    const response = await fetch(`/api/candlesticks/${{symbol}}/${{timeframe}}`);\n    return await response.json();\n}}"
                else:
                    code = f"function {func_name}(marketData) {{\n    console.log('Processing {symbol} market data:', marketData);\n    return marketData.filter(item => item.volume > 1000);\n}}"
                
            elif code_type == 'api_call':
                endpoint = random.choice(self.api_endpoints)
                symbol = random.choice(self.crypto_symbols)
                timeframe = random.choice(self.timeframes)
                code = f"""const fetchMarketData = async () => {{
    try {{
        const response = await fetch(`{endpoint}?symbol={symbol}&timeframe={timeframe}`);
        const data = await response.json();
        console.log('Market data received:', data);
        return data;
    }} catch (error) {{
        console.error('API Error:', error);
        throw error;
    }}
}};"""
                
            elif code_type == 'trading_logic':
                signal_type = random.choice(self.signal_types)
                rsi_value = random.randint(20, 80)
                price = round(random.uniform(100, 50000), 2)
                code = f"""const generateTradingSignal = (rsi, price, volume) => {{
    if (rsi < 30 && volume > 1000000) {{
        return {{
            type: 'BUY',
            confidence: 0.85,
            price: {price},
            reason: 'RSI oversold with high volume'
        }};
    }} else if (rsi > 70) {{
        return {{
            type: 'SELL',
            confidence: 0.75,
            price: {price},
            reason: 'RSI overbought'
        }};
    }}
    return {{ type: 'HOLD', confidence: 0.5, price: {price} }};
}};"""
                
            elif code_type == 'indicator_calc':
                indicator = random.choice(self.indicators)
                period = random.choice([14, 20, 50, 200])
                code = f"""const calculate{indicator} = (prices, period = {period}) => {{
    if (prices.length < period) return null;

    const recentPrices = prices.slice(-period);
    const sum = recentPrices.reduce((acc, price) => acc + price, 0);
    const average = sum / period;

    console.log(`{indicator} ({period}): ${{average.toFixed(2)}}`);
    return average;
}};"""
                    
            elif code_type == 'signal_generation':
                symbol = random.choice(self.crypto_symbols)
                timeframe = random.choice(self.timeframes)
                signal_type = random.choice(self.signal_types)
                confidence = round(random.uniform(0.6, 0.95), 2)
                code = f"""const generateSignal{random.randint(1, 999)} = () => {{
    const signal = {{
        symbol: '{symbol}',
        timeframe: '{timeframe}',
        type: '{signal_type}',
        confidence: {confidence},
        timestamp: new Date().toISOString(),
        indicators: {{
            rsi: {random.randint(20, 80)},
            macd: {round(random.uniform(-5, 5), 3)},
            volume: {random.randint(100000, 10000000)}
        }}
    }};

    console.log('Signal generated:', signal);
    return signal;
}};"""
                
            elif code_type == 'portfolio_management':
                symbols = random.sample(self.crypto_symbols, 3)
                code = f"""const portfolioManager = {{
    holdings: {{
        {symbols[0]}: {{ amount: {round(random.uniform(0.1, 10), 4)}, value: {round(random.uniform(1000, 50000), 2)} }},
        {symbols[1]}: {{ amount: {round(random.uniform(0.1, 10), 4)}, value: {round(random.uniform(1000, 50000), 2)} }},
        {symbols[2]}: {{ amount: {round(random.uniform(0.1, 10), 4)}, value: {round(random.uniform(1000, 50000), 2)} }}
    }},

    getTotalValue() {{
        return Object.values(this.holdings).reduce((total, holding) => total + holding.value, 0);
    }},

    rebalance() {{
        console.log('Rebalancing portfolio...');
        // Portfolio rebalancing logic here
    }}
}};"""

            elif code_type == 'market_analysis':
                symbol = random.choice(self.crypto_symbols)
                code = f"""const analyzeMarket{random.randint(1, 999)} = (candlesticks) => {{
    const analysis = {{
        symbol: '{symbol}',
        trend: 'BULLISH',
        support: {round(random.uniform(20000, 40000), 2)},
        resistance: {round(random.uniform(45000, 60000), 2)},
        volume24h: {random.randint(1000000, 100000000)},
        priceChange24h: {round(random.uniform(-10, 10), 2)}
    }};

    // Detect candlestick patterns
    const patterns = detectPatterns(candlesticks);
    analysis.patterns = patterns;

    return analysis;
}};"""

            elif code_type == 'crypto_class':
                class_name = f"Crypto{random.choice(['Trader', 'Analyzer', 'Monitor', 'Signal'])}{random.randint(1, 99)}"
                symbol = random.choice(self.crypto_symbols)
                code = f"""class {class_name} {{
    constructor(symbol = '{symbol}') {{
        this.symbol = symbol;
        this.isActive = true;
        this.signals = [];
    }}

    async start() {{
        console.log(`Starting {class_name} for ${{this.symbol}}`);
        this.isActive = true;
    }}

    stop() {{
        console.log(`Stopping {class_name} for ${{this.symbol}}`);
        this.isActive = false;
    }}

    addSignal(signal) {{
        this.signals.push({{ ...signal, timestamp: Date.now() }});
    }}
}}"""

            else:  # websocket_handler
                symbol = random.choice(self.crypto_symbols)
                code = f"""const websocketHandler = {{
    connection: null,

    connect() {{
        this.connection = new WebSocket('wss://stream.binance.com:9443/ws/{symbol.lower()}@ticker');

        this.connection.onopen = () => {{
            console.log('WebSocket connected for {symbol}');
        }};

        this.connection.onmessage = (event) => {{
            const data = JSON.parse(event.data);
            this.handlePriceUpdate(data);
        }};

        this.connection.onerror = (error) => {{
            console.error('WebSocket error:', error);
        }};
    }},

    handlePriceUpdate(data) {{
        console.log(`{symbol} price update:`, data.c);
        // Process real-time price data
    }}
}};"""
            
            # Ensure uniqueness
            if code not in self.generated_code:
                self.generated_code.append(code)
                code_snippets.append(code)
        
        return code_snippets
    
    def create_js_file(self):
        """Create a new JavaScript file with unique content"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Crypto-related file names
        crypto_names = [
            'cryptoSignals', 'tradingIndicators', 'marketAnalysis', 'portfolioManager',
            'binanceAPI', 'coinGeckoData', 'candlestickPatterns', 'rsiCalculator',
            'macdIndicator', 'tradingBot', 'priceAlerts', 'volumeAnalysis',
            'supportResistance', 'trendAnalyzer', 'signalGenerator', 'cryptoUtils',
            'marketData', 'tradingStrategy', 'riskManager', 'orderExecutor'
        ]

        base_name = random.choice(crypto_names)
        filename = f"{base_name}_{self.file_counter}_{timestamp}.jsx"
        filepath = os.path.join(self.helper_folder, filename)
        
        # Generate code snippets
        code_snippets = self.generate_complex_js_code()
        
        # Create file header
        header = f"""// Crypto Trading Assistant - Auto-generated Component #{self.file_counter}
// Created: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
// File: {filename}
// Purpose: {base_name.replace('crypto', 'Cryptocurrency ').replace('trading', 'Trading ').title()} Module
// Compatible with: Binance API, CoinGecko API, React Frontend

import React, {{ useState, useEffect }} from 'react';

"""
        
        # Combine all code snippets
        file_content = header + "\n\n".join(code_snippets) + "\n"
        
        # Write to file
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(file_content)
            print(f"✅ Created: {filename} ({len(code_snippets)} code blocks)")
            self.file_counter += 1
            return True
        except Exception as e:
            print(f"❌ Error creating file {filename}: {e}")
            return False
    
    def move_mouse_randomly(self):
        """Move mouse to random position"""
        try:
            screen_width, screen_height = pyautogui.size()
            x = random.randint(100, screen_width - 100)
            y = random.randint(100, screen_height - 100)
            duration = random.uniform(0.8, 2.5)
            pyautogui.moveTo(x, y, duration=duration)
        except Exception as e:
            print(f"Mouse movement error: {e}")
    
    def run(self):
        print("🚀 Random JavaScript File Generator Started!")
        print(f"📁 Files will be created in: {os.path.abspath(self.helper_folder)}")
        print("⏹️  Press SPACE to stop...")
        print("=" * 60)
        
        while self.running:
            try:
                # Create new JS file
                self.create_js_file()
                
                # Move mouse randomly
                self.move_mouse_randomly()
                
                # Wait before next iteration
                wait_time = random.uniform(2, 5)
                time.sleep(wait_time)
                
                # Check if space was pressed
                if keyboard.is_pressed('space'):
                    self.running = False
                    break
                    
            except KeyboardInterrupt:
                self.running = False
                break
            except Exception as e:
                print(f"❌ Unexpected error: {e}")
                time.sleep(1)
        
        print(f"\n🛑 Stopped by user.")
        print(f"📊 Generated {self.file_counter - 1} JavaScript files in '{self.helper_folder}' folder.")
        print(f"💾 Total unique code snippets created: {len(self.generated_code)}")

if __name__ == "__main__":
    # Disable pyautogui failsafe for smooth operation
    pyautogui.FAILSAFE = False
    
    try:
        generator = RandomJSFileGenerator()
        generator.run()
    except KeyboardInterrupt:
        print("\n🛑 Program interrupted by user.")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
