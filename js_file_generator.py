import random
import time
import threading
import pyautogui
import keyboard
import os
from collections import deque
from datetime import datetime

class RandomJSFileGenerator:
    def __init__(self):
        self.running = True
        self.generated_code = deque(maxlen=1000)  # Track last 1000 snippets
        self.file_counter = 1
        self.helper_folder = "Helper"
        
        # Ensure Helper folder exists
        if not os.path.exists(self.helper_folder):
            os.makedirs(self.helper_folder)
        
        # Crypto Trading Assistant specific templates
        self.crypto_variables = ['price', 'volume', 'marketCap', 'signal', 'indicator', 'candlestick', 'trend', 'portfolio', 'balance', 'order', 'trade', 'symbol', 'exchange', 'timeframe', 'rsi', 'macd', 'sma', 'ema', 'bollinger', 'support', 'resistance']
        self.crypto_functions = ['calculateRSI', 'getMarketData', 'analyzeTrend', 'generateSignal', 'executeTrade', 'updatePortfolio', 'fetchCandlesticks', 'calculateSMA', 'calculateEMA', 'detectPattern', 'checkSupport', 'checkResistance']
        self.crypto_symbols = ['BTC', 'ETH', 'ADA', 'SOL', 'DOT', 'LINK', 'MATIC', 'AVAX', 'UNI', 'ATOM', 'XRP', 'LTC', 'BCH', 'BNB', 'DOGE']
        self.timeframes = ['1m', '5m', '15m', '30m', '1h', '4h', '1d', '1w']
        self.indicators = ['RSI', 'MACD', 'SMA', 'EMA', 'BB', 'STOCH', 'ADX', 'CCI', 'MFI', 'OBV']
        self.signal_types = ['BUY', 'SELL', 'HOLD', 'STRONG_BUY', 'STRONG_SELL']
        self.api_endpoints = ['/api/market-data', '/api/signals', '/api/portfolio', '/api/trades', '/api/indicators', '/api/candlesticks']
        self.variables = self.crypto_variables + ['data', 'result', 'config', 'response', 'api', 'cache', 'state']
        self.functions = self.crypto_functions + ['console.log', 'fetch', 'parseInt', 'parseFloat', 'JSON.stringify', 'JSON.parse', 'setTimeout', 'setInterval']
        self.methods = ['push', 'pop', 'slice', 'map', 'filter', 'reduce', 'forEach', 'find', 'includes', 'indexOf', 'join', 'split']
        self.operators = ['+', '-', '*', '/', '%', '==', '!=', '>', '<', '>=', '<=', '&&', '||', '===', '!==']
        self.keywords = ['const', 'let', 'var', 'function', 'return', 'if', 'else', 'for', 'while', 'try', 'catch', 'async', 'await']
        
    def generate_complex_js_code(self):
        """Generate crypto trading assistant specific JavaScript code"""
        code_snippets = []

        # Generate multiple related code snippets for a single file
        num_snippets = random.randint(4, 10)

        for _ in range(num_snippets):
            code_type = random.choice(['crypto_variable', 'crypto_function', 'api_call', 'trading_logic', 'indicator_calc', 'signal_generation', 'portfolio_management', 'market_analysis', 'crypto_class', 'websocket_handler'])
            
            if code_type == 'crypto_variable':
                keyword = random.choice(['const', 'let'])
                var_name = random.choice(self.crypto_variables) + str(random.randint(1, 99))
                value_type = random.choice(['price', 'crypto_array', 'trading_config', 'signal_data'])

                if value_type == 'price':
                    price = round(random.uniform(0.001, 100000), 6)
                    value = str(price)
                elif value_type == 'crypto_array':
                    symbols = random.sample(self.crypto_symbols, random.randint(3, 6))
                    value = f'[{", ".join([f\'"{s}"\' for s in symbols])}]'
                elif value_type == 'trading_config':
                    config_props = []
                    config_props.append(f'symbol: "{random.choice(self.crypto_symbols)}"')
                    config_props.append(f'timeframe: "{random.choice(self.timeframes)}"')
                    config_props.append(f'indicator: "{random.choice(self.indicators)}"')
                    config_props.append(f'threshold: {random.uniform(0.1, 10):.2f}')
                    value = f'{{{", ".join(config_props)}}}'
                else:  # signal_data
                    signal_props = []
                    signal_props.append(f'type: "{random.choice(self.signal_types)}"')
                    signal_props.append(f'confidence: {random.uniform(0.5, 1.0):.2f}')
                    signal_props.append(f'timestamp: Date.now()')
                    signal_props.append(f'price: {round(random.uniform(100, 50000), 2)}')
                    value = f'{{{", ".join(signal_props)}}}'

                code = f"{keyword} {var_name} = {value};"
                
            elif code_type == 'crypto_function':
                func_name = random.choice(self.crypto_functions) + str(random.randint(1, 99))
                symbol = random.choice(self.crypto_symbols)
                timeframe = random.choice(self.timeframes)
                if 'calculate' in func_name.lower():
                    code = f"function {func_name}(prices, period = 14) {{\n    // Calculate {random.choice(self.indicators)} for {symbol}\n    const sum = prices.slice(-period).reduce((a, b) => a + b, 0);\n    return sum / period;\n}}"
                elif 'fetch' in func_name.lower():
                    code = f"async function {func_name}(symbol = '{symbol}', timeframe = '{timeframe}') {{\n    const response = await fetch(`/api/candlesticks/${{symbol}}/${{timeframe}}`);\n    return await response.json();\n}}"
                else:
                    code = f"function {func_name}(marketData) {{\n    console.log('Processing {symbol} market data:', marketData);\n    return marketData.filter(item => item.volume > 1000);\n}}"
                
            elif code_type == 'loop':
                loop_type = random.choice(['for', 'while', 'forEach'])
                if loop_type == 'for':
                    var = random.choice(['i', 'j', 'k', 'index'])
                    limit = random.randint(5, 50)
                    action = random.choice(['console.log', 'process', 'calculate'])
                    code = f"for(let {var} = 0; {var} < {limit}; {var}++) {{\n    {action}({var});\n}}"
                elif loop_type == 'while':
                    condition_var = random.choice(self.variables)
                    code = f"while({condition_var} > 0) {{\n    {condition_var}--;\n    console.log({condition_var});\n}}"
                else:  # forEach
                    arr_name = random.choice(self.variables) + 'Array'
                    code = f"{arr_name}.forEach((item, index) => {{\n    console.log(item, index);\n}});"
                
            elif code_type == 'conditional':
                var1 = random.choice(self.variables) + str(random.randint(1, 9))
                op = random.choice(self.operators)
                var2 = random.choice(self.variables + [str(random.randint(1, 100))])
                action1 = f"console.log('Condition true for {var1}');"
                action2 = f"console.log('Condition false for {var1}');"
                code = f"if({var1} {op} {var2}) {{\n    {action1}\n}} else {{\n    {action2}\n}}"
                
            elif code_type == 'array':
                arr_name = random.choice(self.variables) + 'List'
                method = random.choice(self.methods)
                if method in ['push', 'pop']:
                    code = f"{arr_name}.{method}({random.randint(1, 100)});"
                elif method in ['map', 'filter', 'forEach']:
                    code = f"{arr_name}.{method}((item, index) => {{\n    return item * {random.randint(2, 5)};\n}});"
                else:
                    code = f"const result = {arr_name}.{method}();"
                    
            elif code_type == 'object':
                obj_name = random.choice(['config', 'settings', 'data', 'response']) + str(random.randint(1, 99))
                properties = []
                for _ in range(random.randint(3, 6)):
                    prop = random.choice(['id', 'name', 'value', 'status', 'type', 'timestamp'])
                    val = random.choice([str(random.randint(1, 1000)), f'"{random.choice(["active", "pending", "success", "error"])}"', 'true', 'false'])
                    properties.append(f'    {prop}: {val}')
                properties_str = ',\n'.join(properties)
                code = f"const {obj_name} = {{\n{properties_str}\n}};"
                
            elif code_type == 'async':
                func_name = f"fetch{random.choice(['Data', 'Api', 'Resource'])}{random.randint(1, 99)}"
                url = random.choice(['api/data', 'api/users', 'api/config', 'api/status'])
                code = f"async function {func_name}() {{\n    try {{\n        const response = await fetch('/{url}');\n        const data = await response.json();\n        return data;\n    }} catch (error) {{\n        console.error('Error:', error);\n    }}\n}}"
                
            else:  # class
                class_name = f"{random.choice(['Data', 'Api', 'Config', 'Helper'])}{random.choice(['Manager', 'Handler', 'Service'])}{random.randint(1, 99)}"
                prop = random.choice(self.variables)
                code = f"class {class_name} {{\n    constructor({prop}) {{\n        this.{prop} = {prop};\n    }}\n    \n    process() {{\n        return this.{prop};\n    }}\n}}"
            
            # Ensure uniqueness
            if code not in self.generated_code:
                self.generated_code.append(code)
                code_snippets.append(code)
        
        return code_snippets
    
    def create_js_file(self):
        """Create a new JavaScript file with unique content"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"generated_script_{self.file_counter}_{timestamp}.js"
        filepath = os.path.join(self.helper_folder, filename)
        
        # Generate code snippets
        code_snippets = self.generate_complex_js_code()
        
        # Create file header
        header = f"""// Auto-generated JavaScript file #{self.file_counter}
// Created: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
// File: {filename}

"""
        
        # Combine all code snippets
        file_content = header + "\n\n".join(code_snippets) + "\n"
        
        # Write to file
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(file_content)
            print(f"✅ Created: {filename} ({len(code_snippets)} code blocks)")
            self.file_counter += 1
            return True
        except Exception as e:
            print(f"❌ Error creating file {filename}: {e}")
            return False
    
    def move_mouse_randomly(self):
        """Move mouse to random position"""
        try:
            screen_width, screen_height = pyautogui.size()
            x = random.randint(100, screen_width - 100)
            y = random.randint(100, screen_height - 100)
            duration = random.uniform(0.8, 2.5)
            pyautogui.moveTo(x, y, duration=duration)
        except Exception as e:
            print(f"Mouse movement error: {e}")
    
    def run(self):
        print("🚀 Random JavaScript File Generator Started!")
        print(f"📁 Files will be created in: {os.path.abspath(self.helper_folder)}")
        print("⏹️  Press SPACE to stop...")
        print("=" * 60)
        
        while self.running:
            try:
                # Create new JS file
                self.create_js_file()
                
                # Move mouse randomly
                self.move_mouse_randomly()
                
                # Wait before next iteration
                wait_time = random.uniform(2, 5)
                time.sleep(wait_time)
                
                # Check if space was pressed
                if keyboard.is_pressed('space'):
                    self.running = False
                    break
                    
            except KeyboardInterrupt:
                self.running = False
                break
            except Exception as e:
                print(f"❌ Unexpected error: {e}")
                time.sleep(1)
        
        print(f"\n🛑 Stopped by user.")
        print(f"📊 Generated {self.file_counter - 1} JavaScript files in '{self.helper_folder}' folder.")
        print(f"💾 Total unique code snippets created: {len(self.generated_code)}")

if __name__ == "__main__":
    # Disable pyautogui failsafe for smooth operation
    pyautogui.FAILSAFE = False
    
    try:
        generator = RandomJSFileGenerator()
        generator.run()
    except KeyboardInterrupt:
        print("\n🛑 Program interrupted by user.")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
