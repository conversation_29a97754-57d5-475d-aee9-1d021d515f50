import random
import time
import os
from datetime import datetime

class CryptoJSFileGenerator:
    def __init__(self):
        self.file_counter = 1
        self.helper_folder = "Helper"
        
        # Ensure Helper folder exists
        if not os.path.exists(self.helper_folder):
            os.makedirs(self.helper_folder)
        
        # Crypto Trading Assistant specific templates
        self.crypto_symbols = ['BTC', 'ETH', 'ADA', 'SOL', 'DOT', 'LINK', 'MATIC', 'AVAX', 'UNI', 'ATOM']
        self.timeframes = ['1m', '5m', '15m', '30m', '1h', '4h', '1d']
        self.indicators = ['RSI', 'MACD', 'SMA', 'EMA', 'BB', 'STOCH', 'ADX']
        self.signal_types = ['BUY', 'SELL', 'HOLD', 'STRONG_BUY', 'STRONG_SELL']
        
    def generate_crypto_code(self):
        """Generate crypto trading specific JavaScript/React code"""
        code_snippets = []
        
        # Generate 5-8 code snippets per file
        for _ in range(random.randint(5, 8)):
            code_type = random.choice([
                'trading_signal', 'api_call', 'indicator_calc', 
                'portfolio_component', 'market_data', 'websocket_handler'
            ])
            
            if code_type == 'trading_signal':
                symbol = random.choice(self.crypto_symbols)
                signal = random.choice(self.signal_types)
                confidence = round(random.uniform(0.6, 0.95), 2)
                code = f"""const generateTradingSignal = () => {{
    return {{
        symbol: '{symbol}',
        type: '{signal}',
        confidence: {confidence},
        timestamp: new Date().toISOString(),
        price: {round(random.uniform(100, 50000), 2)},
        indicators: {{
            rsi: {random.randint(20, 80)},
            volume: {random.randint(100000, 10000000)}
        }}
    }};
}};"""
            
            elif code_type == 'api_call':
                symbol = random.choice(self.crypto_symbols)
                timeframe = random.choice(self.timeframes)
                code = f"""const fetchCryptoData = async () => {{
    try {{
        const response = await fetch(`/api/crypto/{symbol}?timeframe={timeframe}`);
        const data = await response.json();
        console.log('{symbol} data received:', data);
        return data;
    }} catch (error) {{
        console.error('API Error:', error);
        throw error;
    }}
}};"""
            
            elif code_type == 'indicator_calc':
                indicator = random.choice(self.indicators)
                period = random.choice([14, 20, 50])
                code = f"""const calculate{indicator} = (prices, period = {period}) => {{
    if (prices.length < period) return null;
    
    const recentPrices = prices.slice(-period);
    const sum = recentPrices.reduce((acc, price) => acc + price, 0);
    const average = sum / period;
    
    return {{
        value: average,
        signal: average > prices[prices.length - 1] ? 'BEARISH' : 'BULLISH',
        timestamp: Date.now()
    }};
}};"""
            
            elif code_type == 'portfolio_component':
                symbols = random.sample(self.crypto_symbols, 3)
                code = f"""const PortfolioComponent = () => {{
    const [portfolio, setPortfolio] = useState({{
        {symbols[0]}: {{ amount: {round(random.uniform(0.1, 10), 4)}, value: {round(random.uniform(1000, 50000), 2)} }},
        {symbols[1]}: {{ amount: {round(random.uniform(0.1, 10), 4)}, value: {round(random.uniform(1000, 50000), 2)} }},
        {symbols[2]}: {{ amount: {round(random.uniform(0.1, 10), 4)}, value: {round(random.uniform(1000, 50000), 2)} }}
    }});
    
    const totalValue = Object.values(portfolio).reduce((sum, holding) => sum + holding.value, 0);
    
    return (
        <div className="portfolio-container">
            <h2>Portfolio Value: ${totalValue.toFixed(2)}</h2>
            {{Object.entries(portfolio).map(([symbol, data]) => (
                <div key={{symbol}} className="holding-item">
                    <span>{symbol}: {data.amount} (${data.value})</span>
                </div>
            ))}}
        </div>
    );
}};"""
            
            elif code_type == 'market_data':
                symbol = random.choice(self.crypto_symbols)
                code = f"""const MarketDataWidget = () => {{
    const [marketData, setMarketData] = useState(null);
    
    useEffect(() => {{
        const fetchData = async () => {{
            const data = {{
                symbol: '{symbol}',
                price: {round(random.uniform(100, 50000), 2)},
                change24h: {round(random.uniform(-10, 10), 2)},
                volume: {random.randint(1000000, 100000000)},
                marketCap: {random.randint(1000000000, 1000000000000)}
            }};
            setMarketData(data);
        }};
        
        fetchData();
        const interval = setInterval(fetchData, 10000); // Update every 10 seconds
        
        return () => clearInterval(interval);
    }}, []);
    
    if (!marketData) return <div>Loading...</div>;
    
    return (
        <div className="market-data-widget">
            <h3>{marketData.symbol}</h3>
            <p>Price: ${marketData.price}</p>
            <p>24h Change: {marketData.change24h}%</p>
            <p>Volume: ${marketData.volume.toLocaleString()}</p>
        </div>
    );
}};"""
            
            else:  # websocket_handler
                symbol = random.choice(self.crypto_symbols)
                code = f"""const useWebSocket = (symbol = '{symbol}') => {{
    const [price, setPrice] = useState(null);
    const [connected, setConnected] = useState(false);
    
    useEffect(() => {{
        const ws = new WebSocket(`wss://stream.binance.com:9443/ws/${{symbol.toLowerCase()}}@ticker`);
        
        ws.onopen = () => {{
            console.log(`WebSocket connected for ${{symbol}}`);
            setConnected(true);
        }};
        
        ws.onmessage = (event) => {{
            const data = JSON.parse(event.data);
            setPrice(parseFloat(data.c));
        }};
        
        ws.onerror = (error) => {{
            console.error('WebSocket error:', error);
            setConnected(false);
        }};
        
        return () => {{
            ws.close();
            setConnected(false);
        }};
    }}, [symbol]);
    
    return {{ price, connected }};
}};"""
            
            code_snippets.append(code)
        
        return code_snippets
    
    def create_crypto_file(self):
        """Create a new crypto-related JavaScript/React file"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Crypto-related file names
        crypto_names = [
            'cryptoSignals', 'tradingIndicators', 'marketAnalysis', 'portfolioManager',
            'binanceAPI', 'coinGeckoData', 'candlestickPatterns', 'rsiCalculator',
            'macdIndicator', 'tradingBot', 'priceAlerts', 'volumeAnalysis',
            'supportResistance', 'trendAnalyzer', 'signalGenerator', 'cryptoUtils'
        ]
        
        base_name = random.choice(crypto_names)
        filename = f"{base_name}_{self.file_counter}_{timestamp}.jsx"
        filepath = os.path.join(self.helper_folder, filename)
        
        # Generate code snippets
        code_snippets = self.generate_crypto_code()
        
        # Create file header
        header = f"""// Crypto Trading Assistant - Auto-generated Component #{self.file_counter}
// Created: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
// File: {filename}
// Purpose: {base_name.replace('crypto', 'Cryptocurrency ').replace('trading', 'Trading ').title()} Module
// Compatible with: Binance API, CoinGecko API, React Frontend

import React, {{ useState, useEffect }} from 'react';

"""
        
        # Combine all code snippets
        file_content = header + "\n\n".join(code_snippets) + "\n\nexport default " + base_name + "Component;\n"
        
        # Write to file
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(file_content)
            print(f"✅ Created: {filename} ({len(code_snippets)} code blocks)")
            self.file_counter += 1
            return True
        except Exception as e:
            print(f"❌ Error creating file {filename}: {e}")
            return False
    
    def run(self, num_files=10):
        print("🚀 Crypto JavaScript File Generator Started!")
        print(f"📁 Files will be created in: {os.path.abspath(self.helper_folder)}")
        print(f"🎯 Generating {num_files} crypto-related files...")
        print("=" * 60)
        
        for i in range(num_files):
            self.create_crypto_file()
            time.sleep(1)  # Small delay between file creation
        
        print(f"\n✅ Successfully generated {num_files} crypto trading files!")
        print(f"📊 All files saved in '{self.helper_folder}' folder.")

if __name__ == "__main__":
    try:
        generator = CryptoJSFileGenerator()
        generator.run(15)  # Generate 15 files
    except Exception as e:
        print(f"❌ Error: {e}")
